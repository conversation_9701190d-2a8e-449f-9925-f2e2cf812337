-- Fix null stock_quantity values in products table
-- This migration ensures all products have a valid stock_quantity value

-- Update any products with null stock_quantity to have 0 (out of stock)
UPDATE products 
SET stock_quantity = 0 
WHERE stock_quantity IS NULL;

-- Make stock_quantity column NOT NULL with default value 0
ALTER TABLE products 
MODIFY COLUMN stock_quantity INT NOT NULL DEFAULT 0;

-- Add index for stock quantity queries (for low stock alerts, etc.)
CREATE INDEX IF NOT EXISTS idx_products_stock_quantity ON products(stock_quantity);

-- Add index for active products with stock for better performance
CREATE INDEX IF NOT EXISTS idx_products_active_stock ON products(is_active, stock_quantity);
