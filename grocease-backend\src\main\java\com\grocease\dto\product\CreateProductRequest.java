package com.grocease.dto.product;

import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CreateProductRequest {
    
    @NotBlank(message = "Product name is required")
    @Size(max = 255, message = "Product name must not exceed 255 characters")
    private String name;
    
    @Size(max = 1000, message = "Description must not exceed 1000 characters")
    private String description;
    
    @NotNull(message = "Price is required")
    @DecimalMin(value = "0.0", inclusive = false, message = "Price must be greater than 0")
    @Digits(integer = 10, fraction = 2, message = "Price must be a valid monetary amount")
    private BigDecimal price;
    
    @DecimalMin(value = "0.0", inclusive = false, message = "Original price must be greater than 0")
    @Digits(integer = 10, fraction = 2, message = "Original price must be a valid monetary amount")
    private BigDecimal originalPrice;
    
    @DecimalMin(value = "0.0", message = "Discount must be 0 or greater")
    @DecimalMax(value = "100.0", message = "Discount must not exceed 100%")
    private BigDecimal discount;
    
    private String image;
    
    @NotNull(message = "Category ID is required")
    private Long categoryId;
    
    @NotBlank(message = "Unit is required")
    @Size(max = 50, message = "Unit must not exceed 50 characters")
    private String unit;
    
    @NotNull(message = "Stock status is required")
    private Boolean inStock;
    
    @DecimalMin(value = "0.0", message = "Rating must be 0 or greater")
    @DecimalMax(value = "5.0", message = "Rating must not exceed 5.0")
    private BigDecimal rating;
    
    @Min(value = 0, message = "Review count must be 0 or greater")
    private Integer reviewCount;

    @Min(value = 0, message = "Stock quantity must be 0 or greater")
    private Integer stockQuantity;

    private List<String> tags;
}
